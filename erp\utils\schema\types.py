"""
Schema type definitions and utility functions
Contains PostgreSQL type mappings, reserved keywords, and helper functions
"""
import re
from typing import Dict, Any, Optional

# PostgreSQL reserved keywords that need to be quoted
POSTGRESQL_RESERVED_KEYWORDS = {
    'table', 'order', 'group', 'user', 'select', 'from', 'where', 'insert',
    'update', 'delete', 'create', 'drop', 'alter', 'index', 'primary', 'key',
    'foreign', 'references', 'constraint', 'unique', 'not', 'null', 'default',
    'check', 'column', 'database', 'schema', 'view', 'trigger', 'function',
    'procedure', 'sequence', 'grant', 'revoke', 'role', 'transaction', 'commit',
    'rollback', 'begin', 'end', 'if', 'then', 'else', 'case', 'when', 'union',
    'intersect', 'except', 'distinct', 'all', 'any', 'some', 'exists', 'in',
    'between', 'like', 'ilike', 'similar', 'escape', 'is', 'and', 'or', 'having',
    'limit', 'offset', 'fetch', 'for', 'with', 'recursive', 'window', 'over',
    'partition', 'range', 'rows', 'unbounded', 'preceding', 'following', 'current',
    'row', 'exclude', 'ties', 'only', 'lateral', 'cross', 'inner', 'left', 'right',
    'full', 'outer', 'join', 'on', 'using', 'natural', 'as', 'asc', 'desc',
    'nulls', 'first', 'last', 'array', 'type', 'cast', 'extract', 'overlay',
    'placing', 'substring', 'position', 'trim', 'leading', 'trailing', 'both',
    'collate', 'at', 'time', 'zone', 'interval', 'year', 'month', 'day', 'hour',
    'minute', 'second', 'timezone_hour', 'timezone_minute', 'localtime',
    'localtimestamp', 'current_date', 'current_time', 'current_timestamp',
    'current_user', 'session_user', 'system_user', 'authorization',
    'binary', 'bit', 'bit_length', 'both', 'char', 'character', 'character_length',
    'char_length', 'coalesce', 'convert', 'dec', 'decimal', 'exists', 'extract',
    'float', 'global', 'int', 'integer', 'interval', 'local', 'lower', 'match',
    'max', 'min', 'national', 'nchar', 'none', 'nullif', 'numeric', 'octet_length',
    'partial', 'real', 'smallint', 'substring', 'sum', 'translate', 'translation',
    'trim', 'upper', 'varchar', 'varying', 'whenever', 'absolute', 'action',
    'add', 'admin', 'after', 'aggregate', 'alias', 'allocate', 'are', 'assertion',
    'at', 'before', 'binary', 'bit', 'blob', 'boolean', 'breadth', 'call',
    'cascade', 'cascaded', 'catalog', 'clob', 'collation', 'completion',
    'connect', 'connection', 'constraints', 'constructor', 'continue', 'corresponding',
    'cube', 'current_path', 'current_role', 'cycle', 'data', 'date', 'deallocate',
    'declare', 'default', 'deferrable', 'deferred', 'depth', 'deref', 'describe',
    'descriptor', 'destroy', 'destructor', 'deterministic', 'dictionary',
    'diagnostics', 'disconnect', 'domain', 'dynamic', 'each', 'equals', 'every',
    'exception', 'external', 'false', 'first', 'found', 'free', 'general', 'get',
    'global', 'go', 'grouping', 'host', 'identity', 'ignore', 'immediate',
    'indicator', 'initialize', 'initially', 'inout', 'input', 'intersect',
    'isolation', 'iterate', 'language', 'large', 'last', 'lateral', 'leading',
    'less', 'level', 'limit', 'local', 'locator', 'map', 'match', 'modify',
    'module', 'names', 'natural', 'nclob', 'new', 'next', 'no', 'none',
    'normalize', 'object', 'off', 'old', 'operation', 'ordinality', 'out',
    'output', 'pad', 'parameter', 'parameters', 'partial', 'path', 'postfix',
    'prefix', 'preorder', 'prepare', 'preserve', 'prior', 'privileges', 'public',
    'read', 'reads', 'recursive', 'ref', 'referencing', 'relative', 'restrict',
    'result', 'return', 'returns', 'role', 'rollup', 'routine', 'row', 'rows',
    'savepoint', 'scope', 'scroll', 'search', 'section', 'session', 'sets',
    'size', 'space', 'specific', 'specifictype', 'sql', 'sqlexception',
    'sqlstate', 'sqlwarning', 'start', 'state', 'statement', 'static', 'structure',
    'system_user', 'temporary', 'terminate', 'than', 'timezone_hour',
    'timezone_minute', 'trailing', 'transaction', 'translation', 'treat',
    'trigger', 'true', 'under', 'unknown', 'unnest', 'usage', 'using', 'value',
    'variable', 'whenever', 'without', 'work', 'write', 'zone'
}


def quote_identifier(identifier: str) -> str:
    """Quote PostgreSQL identifier if it's a reserved keyword or contains special characters"""
    if identifier.lower() in POSTGRESQL_RESERVED_KEYWORDS or not identifier.isidentifier():
        return f'"{identifier}"'
    return identifier


def camel_to_snake_case(name: str) -> str:
    """Convert camelCase to snake_case for database column names"""
    # Insert an underscore before any uppercase letter that follows a lowercase letter or digit
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    # Insert an underscore before any uppercase letter that follows a lowercase letter
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


def get_field_type_mapping() -> Dict[str, str]:
    """Get mapping of ERP field types to PostgreSQL types"""
    return {
        'Char': 'VARCHAR',
        'Text': 'TEXT',
        'Boolean': 'BOOLEAN',
        'Selection': 'VARCHAR',
        'Integer': 'INTEGER',
        'Float': 'REAL',
        'Date': 'DATE',
        'Datetime': 'TIMESTAMP',
        'Many2One': 'UUID',
        'One2One': 'UUID',
        'Reference': 'VARCHAR',
        'Binary': 'BYTEA',
        'Json': 'JSONB',
        'Html': 'TEXT'
    }


def get_postgresql_type_to_erp_mapping() -> Dict[str, str]:
    """Convert PostgreSQL type to ERP field type"""
    return {
        'VARCHAR': 'char',
        'TEXT': 'text',
        'BOOLEAN': 'boolean',
        'INTEGER': 'integer',
        'REAL': 'float',
        'DATE': 'date',
        'TIMESTAMP': 'datetime',
        'UUID': 'char',  # UUIDs are stored as char fields in ERP
        'BYTEA': 'binary',
        'JSONB': 'text'  # JSON stored as text in ERP
    }


def map_field_type_to_postgresql(field_type: str, field_obj=None) -> str:
    """
    Map ERP field type to PostgreSQL type with size considerations
    
    Args:
        field_type: ERP field type name
        field_obj: Optional field object for size information
        
    Returns:
        PostgreSQL type string
    """
    type_mapping = get_field_type_mapping()
    pg_type = type_mapping.get(field_type, 'TEXT')
    
    # Handle size for VARCHAR fields
    if pg_type == 'VARCHAR' and field_obj:
        if hasattr(field_obj, 'size') and field_obj.size:
            pg_type = f'VARCHAR({field_obj.size})'
        elif field_type == 'Char':
            # Default size for Char fields
            pg_type = 'VARCHAR(255)'
        elif field_type == 'Reference':
            # Reference fields need enough space for "model,id"
            pg_type = 'VARCHAR(255)'
    elif pg_type == 'VARCHAR' and field_type == 'Char':
        # Default size for Char fields when no field object
        pg_type = 'VARCHAR(255)'
    elif pg_type == 'VARCHAR' and field_type == 'Reference':
        # Reference fields need enough space for "model,id"
        pg_type = 'VARCHAR(255)'
    
    return pg_type


def pg_type_to_erp_type(pg_type: str) -> str:
    """Convert PostgreSQL type to ERP field type"""
    pg_type_upper = pg_type.upper()
    mapping = get_postgresql_type_to_erp_mapping()
    
    # Handle VARCHAR with size
    if pg_type_upper.startswith('VARCHAR'):
        return 'char'
    
    return mapping.get(pg_type_upper, 'char')  # Default fallback


def get_default_value_sql(field_info: Dict[str, Any]) -> Optional[str]:
    """
    Generate SQL default value from field information
    
    Args:
        field_info: Dictionary containing field information
        
    Returns:
        SQL default value string or None
    """
    if field_info.get('default') is None:
        return None
    
    default_val = field_info['default']
    
    # Handle special SQL functions
    if isinstance(default_val, str) and default_val.startswith(('CURRENT_TIMESTAMP', 'gen_random_uuid()', 'TRUE', 'FALSE')):
        return default_val
    
    # Handle string values
    if isinstance(default_val, str) and not default_val.startswith("'"):
        return f"'{default_val}'"
    
    # Handle boolean values
    if isinstance(default_val, bool):
        return str(default_val).upper()
    
    # Return as-is for other types
    return str(default_val)


def validate_field_info(field_name: str, field_info: Dict[str, Any]) -> bool:
    """
    Validate field information dictionary
    
    Args:
        field_name: Name of the field
        field_info: Field information dictionary
        
    Returns:
        True if valid, False otherwise
    """
    required_keys = ['name', 'type']
    
    for key in required_keys:
        if key not in field_info:
            return False
    
    # Validate field type
    valid_types = get_field_type_mapping().keys()
    if field_info['type'] not in valid_types and not field_info['type'].startswith(('VARCHAR', 'UUID')):
        return False
    
    return True
